"""
Example script demonstrating the complete frequency sweep measurement system.

This script shows how to use the Toptica laser driver and frequency sweep procedure
for automated spectroscopy measurements.
"""

import sys
import time
import numpy as np
from instrument.toptica_ctl import TopticaCTL
from instrument.siglent_spd import SiglentSPD4000X

# Example configuration
LASER_IP = "*************"
POWER_SUPPLY_IP = "*************"
VOLTAGE_SETPOINT = 5.0
POWER_SUPPLY_CHANNEL = 1

# Frequency sweep parameters
START_FREQUENCY = 193.0  # THz
STOP_FREQUENCY = 194.0   # THz
FREQUENCY_STEPS = 21
SETTLING_TIME = 0.5      # seconds


def example_basic_laser_control():
    """
    Example of basic laser control operations.
    """
    print("=== Basic Laser Control Example ===")
    
    try:
        # Connect to laser
        print(f"Connecting to Toptica laser at {LASER_IP}...")
        laser = TopticaCTL(LASER_IP)
        
        # Read system information
        print(f"Connected to: {laser.serial_number}")
        print(f"System health: {laser.system_health}")
        
        # Read current state
        print(f"Current wavelength: {laser.wavelength_actual:.2f} nm")
        print(f"Current frequency: {laser.frequency:.3f} THz")
        print(f"Emission enabled: {laser.emission_enabled}")
        print(f"Current: {laser.current_actual:.1f} mA")
        print(f"Temperature: {laser.temperature_actual:.1f} °C")
        
        # Enable laser emission
        print("\nEnabling laser emission...")
        laser.emission_enabled = True
        time.sleep(1)
        
        # Set wavelength
        target_wavelength = 1550.0
        print(f"Setting wavelength to {target_wavelength} nm...")
        laser.wavelength = target_wavelength
        time.sleep(2)
        
        actual_wavelength = laser.wavelength_actual
        print(f"Actual wavelength: {actual_wavelength:.2f} nm")
        
        # Set frequency
        target_frequency = 193.5
        print(f"Setting frequency to {target_frequency} THz...")
        laser.frequency = target_frequency
        time.sleep(2)
        
        actual_frequency = laser.frequency
        print(f"Actual frequency: {actual_frequency:.3f} THz")
        
        # Disable emission and shutdown
        print("\nDisabling emission and shutting down...")
        laser.emission_enabled = False
        laser.shutdown()
        
        print("✓ Basic laser control example completed successfully")
        
    except Exception as e:
        print(f"✗ Error in basic laser control: {e}")


def example_frequency_scanning():
    """
    Example of frequency scanning operations.
    """
    print("\n=== Frequency Scanning Example ===")
    
    try:
        # Connect to laser
        print(f"Connecting to Toptica laser at {LASER_IP}...")
        laser = TopticaCTL(LASER_IP)
        
        # Enable emission
        laser.emission_enabled = True
        time.sleep(1)
        
        # Configure frequency sweep
        print(f"Configuring frequency sweep: {START_FREQUENCY} to {STOP_FREQUENCY} THz")
        laser.setup_sweep(
            start_freq=START_FREQUENCY,
            stop_freq=STOP_FREQUENCY,
            scan_speed=1.0,  # THz/s
            output_channel='piezo'
        )
        
        # Start sweep
        print("Starting frequency sweep...")
        laser.start_sweep()
        
        # Monitor for a few seconds
        for i in range(5):
            time.sleep(1)
            wavelength = laser.wavelength_actual
            frequency = laser.frequency
            print(f"  t={i+1}s: {wavelength:.2f} nm ({frequency:.3f} THz)")
        
        # Stop sweep
        print("Stopping frequency sweep...")
        laser.stop_sweep()
        
        # Shutdown
        laser.emission_enabled = False
        laser.shutdown()
        
        print("✓ Frequency scanning example completed successfully")
        
    except Exception as e:
        print(f"✗ Error in frequency scanning: {e}")


def example_multi_instrument_coordination():
    """
    Example of coordinating multiple instruments for a measurement.
    """
    print("\n=== Multi-Instrument Coordination Example ===")
    
    try:
        # Connect to instruments
        print("Connecting to instruments...")
        laser = TopticaCTL(LASER_IP)
        power_supply = SiglentSPD4000X(f"TCPIP::{POWER_SUPPLY_IP}::INSTR")
        channel = power_supply.channels[POWER_SUPPLY_CHANNEL]
        
        print(f"Laser: {laser.serial_number}")
        print(f"Power supply connected")
        
        # Configure power supply
        print(f"Setting power supply CH{POWER_SUPPLY_CHANNEL} to {VOLTAGE_SETPOINT}V...")
        channel.current_limit = 1.0
        channel.voltage = VOLTAGE_SETPOINT
        channel.output_enabled = True
        
        # Configure laser
        print("Enabling laser emission...")
        laser.emission_enabled = True
        time.sleep(1)
        
        # Perform frequency sweep with power supply monitoring
        frequency_points = np.linspace(START_FREQUENCY, STOP_FREQUENCY, FREQUENCY_STEPS)
        
        print(f"Performing {FREQUENCY_STEPS}-point frequency sweep...")
        results = []
        
        for i, freq in enumerate(frequency_points):
            print(f"Point {i+1}/{FREQUENCY_STEPS}: {freq:.3f} THz")
            
            # Set laser frequency
            laser.frequency = freq
            time.sleep(SETTLING_TIME)
            
            # Read measurements
            actual_freq = laser.frequency
            actual_wavelength = laser.wavelength_actual
            voltage = channel.measure_voltage
            current = channel.measure_current
            
            # Store results
            result = {
                'frequency_thz': actual_freq,
                'wavelength_nm': actual_wavelength,
                'voltage_v': voltage,
                'current_a': current
            }
            results.append(result)
            
            print(f"  Actual: {actual_freq:.3f} THz ({actual_wavelength:.2f} nm)")
            print(f"  Power: {voltage:.2f}V, {current:.3f}A")
        
        # Display summary
        print("\nMeasurement Summary:")
        print("Point | Frequency (THz) | Wavelength (nm) | Voltage (V) | Current (A)")
        print("-" * 70)
        for i, result in enumerate(results):
            print(f"{i+1:5d} | {result['frequency_thz']:11.3f} | "
                  f"{result['wavelength_nm']:11.2f} | "
                  f"{result['voltage_v']:9.2f} | {result['current_a']:9.3f}")
        
        # Shutdown instruments
        print("\nShutting down instruments...")
        laser.emission_enabled = False
        laser.shutdown()
        power_supply.shutdown()
        
        print("✓ Multi-instrument coordination example completed successfully")
        
    except Exception as e:
        print(f"✗ Error in multi-instrument coordination: {e}")


def example_error_handling():
    """
    Example of proper error handling and recovery.
    """
    print("\n=== Error Handling Example ===")
    
    try:
        # Connect to laser
        laser = TopticaCTL(LASER_IP)
        
        # Test system health monitoring
        health = laser.system_health
        print(f"System health: {health}")
        
        # Test error checking
        laser.check_errors()
        print("✓ Error checking passed")
        
        # Test graceful shutdown even with errors
        try:
            # This might fail if laser is not in proper state
            laser.emission_enabled = True
        except Exception as e:
            print(f"Expected error during emission control: {e}")
        
        # Shutdown should still work
        laser.shutdown()
        print("✓ Graceful shutdown completed")
        
    except Exception as e:
        print(f"Error in error handling example: {e}")


def main():
    """
    Run all examples to demonstrate the complete system.
    """
    print("Toptica Laser Driver and Frequency Sweep Examples")
    print("=" * 60)
    print("Note: These examples require actual hardware connections.")
    print("Modify IP addresses and parameters as needed for your setup.")
    print("=" * 60)
    
    # Run examples
    example_basic_laser_control()
    example_frequency_scanning()
    example_multi_instrument_coordination()
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("All examples completed!")
    print("\nTo run the full GUI application:")
    print("python procedure/frequency_sweep_procedure.py")


if __name__ == "__main__":
    # Check if running with mock flag
    if len(sys.argv) > 1 and sys.argv[1] == "--mock":
        print("Running in mock mode (no actual hardware required)")
        # Import and run the test instead
        from test_toptica_integration import main as test_main
        test_main()
    else:
        print("Running with real hardware (modify IP addresses as needed)")
        main()
