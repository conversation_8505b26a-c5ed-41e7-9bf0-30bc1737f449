"""
Test script for Toptica CTL laser driver integration.

This script tests the Toptica laser driver implementation without requiring
actual hardware by using mock objects.
"""

import sys
import time
from unittest.mock import Mock, MagicMock, patch

# Mock the Toptica SDK before importing our driver
sys.modules['toptica'] = Mock()
sys.modules['toptica.lasersdk'] = Mock()
sys.modules['toptica.lasersdk.client'] = Mock()

# Create mock classes
class MockClient:
    def __init__(self, connection):
        self.connection = connection
        self._is_open = False
        self._responses = {
            'serial-number': 'CTL-001',
            'system-type': 'CTL',
            'system-health-txt': 'OK',
            'laser1:emission': False,
            'laser1:ctl:wavelength-act': 1550.0,
            'laser1:dl:cc:current-act': 100.0,
            'laser1:dl:tc:temp-act': 25.0,
            'laser1:dl:pc:power-act': 10.0,
            'system-label': 'Test Laser'
        }
        self._commands = []
    
    def open(self):
        self._is_open = True
        print("Mock: Opened connection to laser")
    
    def close(self):
        self._is_open = False
        print("Mock: Closed connection to laser")
    
    def get(self, param):
        self._commands.append(f"GET {param}")
        value = self._responses.get(param, 0.0)
        print(f"Mock GET: {param} -> {value}")
        return value
    
    def set(self, param, value):
        self._commands.append(f"SET {param} = {value}")
        self._responses[param] = value
        print(f"Mock SET: {param} = {value}")
    
    def exec(self, command):
        self._commands.append(f"EXEC {command}")
        print(f"Mock EXEC: {command}")
    
    def change_ul(self, user_level, password):
        print(f"Mock: Changed user level to {user_level}")

class MockNetworkConnection:
    def __init__(self, address):
        self.address = address
        print(f"Mock: Created network connection to {address}")

class MockUserLevel:
    READONLY = 1
    NORMAL = 2
    MAINTENANCE = 3
    SERVICE = 4

class MockDecopError(Exception):
    pass

# Set up the mocks
sys.modules['toptica.lasersdk.client'].Client = MockClient
sys.modules['toptica.lasersdk.client'].NetworkConnection = MockNetworkConnection
sys.modules['toptica.lasersdk.client'].UserLevel = MockUserLevel
sys.modules['toptica.lasersdk.client'].DecopError = MockDecopError

# Now import our driver
from instrument.toptica_ctl import TopticaCTL


def test_basic_connection():
    """Test basic connection and initialization."""
    print("=== Testing Basic Connection ===")
    
    try:
        laser = TopticaCTL("*************")
        print("✓ Successfully created TopticaCTL instance")
        
        # Test basic properties
        serial = laser.serial_number
        print(f"✓ Serial number: {serial}")
        
        health = laser.system_health
        print(f"✓ System health: {health}")
        
        laser.shutdown()
        print("✓ Successfully shutdown laser")
        
    except Exception as e:
        print(f"✗ Connection test failed: {e}")
        return False
    
    return True


def test_wavelength_control():
    """Test wavelength control functionality."""
    print("\n=== Testing Wavelength Control ===")
    
    try:
        laser = TopticaCTL("*************")
        
        # Test reading wavelength
        wavelength = laser.wavelength_actual
        print(f"✓ Current wavelength: {wavelength} nm")
        
        # Test setting wavelength
        laser.wavelength = 1555.0
        print("✓ Set wavelength to 1555.0 nm")
        
        # Test frequency property
        freq = laser.frequency
        print(f"✓ Current frequency: {freq:.3f} THz")

        # Test setting frequency
        laser.frequency = 193.5
        print("✓ Set frequency to 193.5 THz")
        
        laser.shutdown()
        
    except Exception as e:
        print(f"✗ Wavelength control test failed: {e}")
        return False
    
    return True


def test_emission_control():
    """Test laser emission control."""
    print("\n=== Testing Emission Control ===")
    
    try:
        laser = TopticaCTL("*************")
        
        # Test reading emission state
        emission = laser.emission_enabled
        print(f"✓ Emission state: {emission}")
        
        # Test enabling emission
        laser.emission_enabled = True
        print("✓ Enabled laser emission")
        
        # Test disabling emission
        laser.emission_enabled = False
        print("✓ Disabled laser emission")
        
        laser.shutdown()
        
    except Exception as e:
        print(f"✗ Emission control test failed: {e}")
        return False
    
    return True


def test_current_temperature_control():
    """Test current and temperature control."""
    print("\n=== Testing Current and Temperature Control ===")
    
    try:
        laser = TopticaCTL("*************")
        
        # Test reading current
        current = laser.current_actual
        print(f"✓ Current: {current} mA")
        
        # Test setting current
        laser.current_setpoint = 120.0
        print("✓ Set current setpoint to 120.0 mA")
        
        # Test reading temperature
        temp = laser.temperature_actual
        print(f"✓ Temperature: {temp} °C")
        
        # Test setting temperature
        laser.temperature_setpoint = 26.0
        print("✓ Set temperature setpoint to 26.0 °C")
        
        laser.shutdown()
        
    except Exception as e:
        print(f"✗ Current/temperature control test failed: {e}")
        return False
    
    return True


def test_scanning_functionality():
    """Test frequency scanning functionality."""
    print("\n=== Testing Scanning Functionality ===")
    
    try:
        laser = TopticaCTL("*************")
        
        # Test setup sweep
        laser.setup_sweep(
            start_freq=193.0,
            stop_freq=194.0,
            scan_speed=1.0,
            output_channel='piezo'
        )
        print("✓ Configured frequency sweep")
        
        # Test start sweep
        laser.start_sweep()
        print("✓ Started frequency sweep")
        
        # Test stop sweep
        laser.stop_sweep()
        print("✓ Stopped frequency sweep")
        
        laser.shutdown()
        
    except Exception as e:
        print(f"✗ Scanning functionality test failed: {e}")
        return False
    
    return True


def test_error_handling():
    """Test error handling functionality."""
    print("\n=== Testing Error Handling ===")
    
    try:
        laser = TopticaCTL("*************")
        
        # Test check_errors method
        laser.check_errors()
        print("✓ Error checking works")
        
        # Test invalid command handling
        try:
            laser.write("invalid:command:format")
            print("✓ Invalid command handled gracefully")
        except Exception:
            print("✓ Invalid command properly rejected")
        
        laser.shutdown()
        
    except Exception as e:
        print(f"✗ Error handling test failed: {e}")
        return False
    
    return True


def test_frequency_conversion():
    """Test frequency/wavelength conversion."""
    print("\n=== Testing Frequency/Wavelength Conversion ===")

    try:
        # Test conversion constants
        c = 299.792458  # THz·nm

        # Test wavelength to frequency
        wavelength = 1550.0  # nm
        expected_freq = c / wavelength  # THz

        laser = TopticaCTL("*************")
        laser.client._responses['laser1:ctl:wavelength-act'] = wavelength

        actual_freq = laser.frequency
        print(f"✓ Wavelength {wavelength} nm -> Frequency {actual_freq:.3f} THz")

        # Test frequency to wavelength
        frequency = 193.5  # THz
        expected_wavelength = c / frequency  # nm

        laser.frequency = frequency
        print(f"✓ Frequency {frequency} THz -> Wavelength {expected_wavelength:.2f} nm")

        laser.shutdown()

    except Exception as e:
        print(f"✗ Frequency conversion test failed: {e}")
        return False

    return True


def main():
    """Run all tests."""
    print("Testing Toptica CTL Laser Driver Implementation")
    print("=" * 60)
    
    tests = [
        test_basic_connection,
        test_wavelength_control,
        test_emission_control,
        test_current_temperature_control,
        test_scanning_functionality,
        test_error_handling,
        test_frequency_conversion
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The Toptica driver implementation is working correctly.")
        print("\nKey features demonstrated:")
        print("- Toptica SDK integration with PyMeasure")
        print("- Wavelength and frequency control")
        print("- Laser emission control")
        print("- Current and temperature monitoring/control")
        print("- Frequency scanning capabilities")
        print("- Proper error handling and validation")
        print("- Frequency/wavelength conversion")
    else:
        print(f"✗ {total - passed} tests failed. Please review the implementation.")


if __name__ == "__main__":
    main()
