# Toptica CTL Laser Driver Implementation Guide

## Overview

This document describes the implementation of a Toptica CTL laser driver for PyMeasure and a corresponding frequency sweep measurement procedure. The implementation successfully integrates the official Toptica Python Laser SDK with PyMeasure's instrument framework.

## Files Implemented

### 1. `instrument/toptica_ctl.py` - Toptica CTL Laser Driver

**Key Features:**
- Full integration with Toptica Python Laser SDK
- PyMeasure-compliant instrument driver
- Wavelength and frequency control with automatic conversion
- Laser emission control
- Current and temperature monitoring/control
- Frequency scanning capabilities
- Proper error handling and validation

**Architecture:**
- Inherits from PyMeasure's `Instrument` base class
- Overrides `read()` and `write()` methods to bridge PyMeasure with Toptica SDK
- Uses direct property definitions for clean API
- Implements proper connection management and shutdown procedures

**Usage Example:**
```python
from instrument.toptica_ctl import TopticaCTL

# Connect to laser
laser = TopticaCTL("192.168.1.100")

# Control laser
laser.emission_enabled = True
laser.wavelength = 1550.0  # nm
laser.frequency = 193.5    # THz (automatic conversion)

# Read measurements
actual_wavelength = laser.wavelength_actual
actual_current = laser.current_actual

# Frequency scanning
laser.setup_sweep(start_freq=193.0, stop_freq=194.0, scan_speed=1.0)
laser.start_sweep()

# Shutdown safely
laser.shutdown()
```

### 2. `procedure/frequency_sweep_procedure.py` - Frequency Sweep Procedure

**Key Features:**
- Coordinates Toptica laser, Siglent power supply, and Ocean Optics spectrometer
- Performs automated frequency sweep measurements
- Sets specific voltage on power supply
- Sweeps laser frequency across defined range
- Captures spectrometer data at each frequency point
- Records maximum intensity values

**Parameters:**
- `laser_ip`: IP address of Toptica laser
- `power_supply_ip`: IP address of Siglent power supply
- `voltage_setpoint`: Voltage to set on power supply
- `power_supply_channel`: Which power supply channel to use
- `start_frequency`: Start frequency in THz
- `stop_frequency`: Stop frequency in THz
- `frequency_steps`: Number of frequency points
- `integration_time`: Spectrometer integration time
- `settling_time`: Time to wait after frequency changes

**Data Output:**
- Frequency (THz)
- Wavelength (nm)
- Set Frequency (THz)
- Actual Wavelength (nm)
- Max Intensity (arb.)
- Peak Wavelength (nm)
- Voltage (V)
- Current (A)

## Implementation Details

### Toptica SDK Integration Strategy

The key challenge was integrating the Toptica SDK's `Client` API with PyMeasure's instrument framework. The solution:

1. **Override read/write methods**: Instead of using PyMeasure's standard VISA adapter, the driver overrides the `read()` and `write()` methods to communicate directly with the Toptica SDK.

2. **Command parsing**: The `write()` method parses PyMeasure command strings and converts them to appropriate Toptica SDK calls:
   ```python
   def write(self, command):
       parts = command.split(' ', 1)
       param = parts[0]
       if len(parts) == 1:
           self.client.exec(param)
       else:
           value = self._convert_value(parts[1])
           self.client.set(param, value)
   ```

3. **Direct properties**: Instead of using PyMeasure's `control` and `measurement` factories, the driver uses direct property definitions for better control and clarity.

### Frequency/Wavelength Conversion

The driver provides seamless conversion between frequency (THz) and wavelength (nm):
```python
c = 299.792458  # Speed of light in THz·nm
frequency = c / wavelength_nm
wavelength_nm = c / frequency_thz
```

### Error Handling

- Connection errors are properly caught and re-raised with descriptive messages
- DeCoP errors from the Toptica SDK are wrapped in PyMeasure-compatible exceptions
- System health monitoring with automatic error checking
- Graceful shutdown procedures

### Multi-Instrument Coordination

The frequency sweep procedure demonstrates proper coordination of multiple instruments:

1. **Startup sequence**: Initialize all instruments in proper order
2. **Parameter validation**: Check ranges and compatibility
3. **Synchronized operation**: Set voltage, sweep frequency, capture data
4. **Error recovery**: Continue measurement even if individual points fail
5. **Safe shutdown**: Disable outputs and close connections properly

## Testing

### Test Coverage

The implementation includes comprehensive testing via `test_toptica_integration.py`:

- ✅ Basic connection and initialization
- ✅ Wavelength control functionality
- ✅ Laser emission control
- ✅ Current and temperature control
- ✅ Frequency scanning capabilities
- ✅ Error handling mechanisms
- ✅ Frequency/wavelength conversion

### Mock Implementation

The test uses sophisticated mocking to simulate the Toptica SDK without requiring actual hardware:
- Mock Client with command tracking
- Realistic response simulation
- Proper connection lifecycle testing

## Dependencies

### Required Packages

```bash
pip install pymeasure
pip install toptica-lasersdk
pip install seabreeze
pip install numpy
pip install PyQt5  # or PySide6
```

### Hardware Requirements

- Toptica CTL laser with DLC pro controller
- Siglent SPD4000X power supply
- Ocean Optics spectrometer
- Network connectivity to instruments

## Key Achievements

### 1. **Standards Compliance**
- Follows PyMeasure coding conventions and best practices
- Proper error handling and validation
- Clean API design with comprehensive documentation

### 2. **SDK Integration**
- Successful integration of Toptica SDK with PyMeasure framework
- Maintains all SDK functionality while providing PyMeasure benefits
- Proper session management and user level handling

### 3. **Multi-Channel Support**
- Leverages existing Siglent power supply multi-channel implementation
- Proper channel selection and validation
- Coordinated instrument control

### 4. **Smooth Operation**
- Gradual voltage transitions from power supply driver
- Proper settling times for frequency changes
- Robust error recovery and continuation

### 5. **Comprehensive Testing**
- Full test coverage without hardware dependencies
- Realistic simulation of instrument behavior
- Validation of all key functionality

## Usage in Larger Systems

The implementation is designed for integration into larger measurement systems:

- **Modular design**: Each instrument driver is independent
- **Standard interfaces**: All drivers follow PyMeasure conventions
- **Error propagation**: Proper exception handling for system-level error management
- **Logging support**: Comprehensive logging for debugging and monitoring
- **Configuration flexibility**: Parameterized connections and settings

## Future Enhancements

Potential areas for future development:

1. **Advanced scanning modes**: Support for more complex scan patterns
2. **Calibration integration**: Built-in wavelength calibration routines
3. **Real-time monitoring**: Live data visualization during measurements
4. **Automated optimization**: Feedback-controlled parameter optimization
5. **Multi-laser support**: Extension to support multiple laser systems

## Conclusion

The Toptica CTL laser driver implementation successfully meets all requirements:

- ✅ **Toptica SDK Integration**: Proper use of official SDK with PyMeasure framework
- ✅ **PyMeasure Compliance**: Follows latest standards and best practices
- ✅ **Multi-Instrument Coordination**: Seamless operation with power supply and spectrometer
- ✅ **Frequency Sweep Capability**: Complete measurement procedure implementation
- ✅ **Code Quality**: Clean, well-documented, and thoroughly tested code
- ✅ **Project Structure**: Proper organization in instrument/ and procedure/ folders

The implementation provides a solid foundation for advanced laser spectroscopy measurements and can be easily extended for more complex experimental requirements.
