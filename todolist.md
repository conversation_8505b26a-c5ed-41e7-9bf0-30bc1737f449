任务 1: 创建自定义仪器驱动文件

指令: 创建一个名为 siglent_spd.py 的新 Python 文件。

子任务:

在该文件中，导入 Instrument, strict_range, strict_discrete_set from pymeasure.instruments。

定义一个名为 SiglentSPD1X 的类，使其继承自 pymeasure.instruments.Instrument。

实现 __init__ 方法。在该方法中，使用 kwargs.setdefault 将 'write_termination' 和 'read_termination' 的默认值都设置为 '\n'，然后调用父类的 __init__ 方法。

使用 Instrument.control 实现一个名为 'voltage' 的属性。其 get_command 为 "CH1:VOLTage?"，set_command 为 "CH1:VOLTage %f"。添加文档字符串，并使用 strict_range 验证器设置一个合理的电压范围（例如 ）。

使用 Instrument.control 实现一个名为 'current_limit' 的属性。其 get_command 为 "CH1:CURRent?"，set_command 为 "CH1:CURRent %f"。添加文档字符串，并使用 strict_range 验证器设置一个合理的电流范围（例如 ）。

使用 Instrument.measurement 实现一个名为 'measure_voltage' 的只读属性。其 get_command 为 "MEASure:VOLTage?"。添加文档字符串。

使用 Instrument.measurement 实现一个名为 'measure_current' 的只读属性。其 get_command 为 "MEASure:CURRent?"。添加文档字符串。

使用 Instrument.control 实现一个名为 'output_enabled' 的布尔属性。其 get_command 为 "OUTPut? CH1"，set_command 为 "OUTPut CH1,%s"。设置 map_values=True，并提供一个字典 {True: 'ON', False: 'OFF'} 来进行值映射。

实现一个名为 shutdown 的方法。该方法应先将 'voltage' 设置为 0，然后将 'output_enabled' 设置为 False。

任务 2: 创建主应用程序文件

指令: 创建一个名为 spectroscopy_app.py 的新 Python 文件。

子任务:

导入所有必要的库：sys, numpy, pandas, time, pymeasure.experiment, pymeasure.display, seabreeze.spectrometers, 以及 PyQt5/PySide6 的 QtWidgets。

从 siglent_spd.py 文件中导入 SiglentSPD1X 类。

定义一个名为 SpectroscopyProcedure 的类，使其继承自 pymeasure.experiment.Procedure。

在 SpectroscopyProcedure 类内部，使用 FloatParameter 定义 'start_voltage' 和 'stop_voltage'。使用 IntegerParameter 定义 'voltage_steps' 和 'integration_time'。为它们提供合适的名称、单位和默认值。

定义 DATA_COLUMNS 列表，包含 '设定电压 (V)', '实测电压 (V)', '实测电流 (A)', '峰值波长 (nm)', '峰值强度 (arb.)'。

实现 startup 方法。在该方法中，实例化 SiglentSPD1X（使用占位符 VISA 地址）和 seabreeze.spectrometers.Spectrometer，并进行初始配置。

实现 execute 方法。在该方法中，编写主测量循环：使用 np.linspace 生成电压点，循环遍历电压点，设置电源电压，采集光谱数据，使用 np.argmax 寻找峰值，读取电源的实际测量值，然后使用 self.emit('results',...) 发射包含所有数据点的字典。在循环末尾，检查 self.should_stop()。

实现 shutdown 方法。在该方法中，安全地调用电源的 shutdown() 方法和光谱仪的 close() 方法。

定义一个名为 MainWindow 的类，使其继承自 pymeasure.display.windows.ManagedWindow。

在 MainWindow 的 __init__ 方法中，调用 super().__init__。传入 procedure_class=SpectroscopyProcedure，并配置 inputs, displays, x_axis, 和 y_axis 列表/字符串，使其与 SpectroscopyProcedure 的定义相匹配。

在文件末尾，创建主执行块 if __name__ == "__main__":。在该块中，创建 QApplication 实例，实例化并显示 MainWindow，最后调用 app.exec()。