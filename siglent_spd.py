"""
Siglent SPD1X Series Power Supply Driver

This module provides a PyMeasure driver for Siglent SPD1X series power supplies
with proper multi-channel support using PyMeasure's Channel architecture.
"""

from pymeasure.instruments import Instrument, Channel, strict_range, strict_discrete_set


class SiglentSPD1XChannel(Channel):
    """
    Represents a single channel of the Siglent SPD1X power supply.
    
    This class implements the individual channel controls for voltage, current,
    and output state using PyMeasure's Channel base class for proper multi-channel support.
    """
    
    voltage = Instrument.control(
        "CH{ch}:VOLTage?", "CH{ch}:VOLTage %f",
        """Control the output voltage of the channel in Volts.
        
        The voltage range is typically 0-30V for most SPD1X models.
        Check your specific model's specifications for exact limits.
        """,
        validator=strict_range,
        values=(0, 15)
    )
    
    current_limit = Instrument.control(
        "CH{ch}:CURRent?", "CH{ch}:CURRent %f", 
        """Control the current limit of the channel in Amperes.
        
        The current range is typically 0-3A for most SPD1X models.
        Check your specific model's specifications for exact limits.
        """,
        validator=strict_range,
        values=(0, 1)
    )
    
    measure_voltage = Instrument.measurement(
        "MEASure:VOLTage? CH{ch}",
        """Measure the actual output voltage of the channel in Volts."""
    )
    
    measure_current = Instrument.measurement(
        "MEASure:CURRent? CH{ch}",
        """Measure the actual output current of the channel in Amperes."""
    )
    
    output_enabled = Instrument.control(
        "OUTPut? CH{ch}", "OUTPut CH{ch},%s",
        """Control the output state of the channel.
        
        True enables the output, False disables it.
        """,
        map_values=True,
        values={True: 'ON', False: 'OFF'}
    )
    
    def shutdown(self):
        """
        Safely shutdown this channel by setting voltage to 0 and disabling output.
        """
        self.voltage = 0
        self.output_enabled = False


class SiglentSPD1X(Instrument):
    """
    Siglent SPD1X Series Power Supply Driver
    
    This class provides a PyMeasure driver for Siglent SPD1X series power supplies
    with proper multi-channel support. The SPD1X series typically has 2 channels.
    
    Example usage:
        # Connect to the power supply
        psu = SiglentSPD1X("TCPIP::*************::INSTR")
        
        # Access individual channels
        psu.ch_1.voltage = 5.0
        psu.ch_1.current_limit = 1.0
        psu.ch_1.output_enabled = True
        
        # Read measurements
        actual_voltage = psu.ch_1.measure_voltage
        actual_current = psu.ch_1.measure_current
        
        # Access channels through collection
        psu.channels[1].voltage = 3.3
        psu.channels[2].voltage = 12.0
        
        # Shutdown all channels safely
        psu.shutdown()
    """
    
    def __init__(self, adapter, name="Siglent SPD1X Power Supply", **kwargs):
        """
        Initialize the Siglent SPD1X power supply.
        
        Args:
            adapter: PyMeasure adapter for communication (VISA resource string or adapter object)
            name: Name of the instrument
            **kwargs: Additional keyword arguments passed to parent Instrument class
        """
        # Set default termination characters for Siglent instruments
        kwargs.setdefault('write_termination', '\n')
        kwargs.setdefault('read_termination', '\n')
        
        super().__init__(adapter, name, **kwargs)
    
    # Create two channels for the SPD1X series (typically has 2 channels)
    channels = Instrument.MultiChannelCreator(SiglentSPD1XChannel, [1, 2])
    
    def shutdown(self):
        """
        Safely shutdown all channels of the power supply.
        
        This method iterates through all channels and calls their individual
        shutdown methods to ensure safe power-down.
        """
        for channel in self.channels.values():
            channel.shutdown()
