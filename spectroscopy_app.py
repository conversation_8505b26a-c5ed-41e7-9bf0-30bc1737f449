"""
Spectroscopy Application with Siglent SPD1X Power Supply

This application provides a complete spectroscopy measurement system using
a Siglent SPD1X power supply and Ocean Optics spectrometer.
"""

import sys
import numpy as np
import pandas as pd
import time
from pymeasure.experiment import Procedure, FloatParameter, IntegerParameter
from pymeasure.display import ManagedWindow
import seabreeze.spectrometers
try:
    from PyQt5.QtWidgets import QApplication
except ImportError:
    from PySide6.QtWidgets import QApplication

from siglent_spd import SiglentSPD4000X


class SpectroscopyProcedure(Procedure):
    """
    Procedure for automated spectroscopy measurements with voltage sweeping.
    
    This procedure sweeps the voltage on a power supply channel while
    simultaneously acquiring spectra from a connected spectrometer.
    """
    
    # Measurement parameters
    start_voltage = FloatParameter(
        'Start Voltage', 
        units='V', 
        default=0.0,
        minimum=0.0,
        maximum=30.0
    )
    
    stop_voltage = FloatParameter(
        'Stop Voltage', 
        units='V', 
        default=5.0,
        minimum=0.0,
        maximum=30.0
    )
    
    voltage_steps = IntegerParameter(
        'Voltage Steps', 
        default=11,
        minimum=2,
        maximum=1000
    )
    
    integration_time = IntegerParameter(
        'Integration Time', 
        units='ms', 
        default=100,
        minimum=1,
        maximum=10000
    )
    
    channel_number = IntegerParameter(
        'Power Supply Channel',
        default=1,
        minimum=1,
        maximum=2
    )
    
    # Data columns for results
    DATA_COLUMNS = [
        '设定电压 (V)',      # Set Voltage (V)
        '实测电压 (V)',      # Measured Voltage (V) 
        '实测电流 (A)',      # Measured Current (A)
        '峰值波长 (nm)',     # Peak Wavelength (nm)
        '峰值强度 (arb.)'    # Peak Intensity (arb.)
    ]
    
    def startup(self):
        """
        Initialize instruments and configure measurement setup.
        """
        # Initialize power supply (replace with actual VISA address)
        self.power_supply = SiglentSPD4000X("TCPIP::*************::INSTR")
        
        # Get the specified channel
        self.channel = self.power_supply.channels[self.channel_number]
        
        # Initialize spectrometer
        devices = seabreeze.spectrometers.list_devices()
        if not devices:
            raise RuntimeError("No spectrometer found. Please connect a spectrometer.")
        
        self.spectrometer = seabreeze.spectrometers.Spectrometer(devices[0])
        
        # Configure spectrometer
        self.spectrometer.integration_time_micros(self.integration_time * 1000)  # Convert ms to μs
        
        # Initial power supply configuration
        self.channel.current_limit = 1.0  # Set reasonable current limit
        self.channel.voltage = 0.0        # Start at 0V
        self.channel.output_enabled = True
        
        # Allow settling time
        time.sleep(0.5)
    
    def execute(self):
        """
        Execute the main measurement loop.
        """
        # Generate voltage points
        voltage_points = np.linspace(
            self.start_voltage, 
            self.stop_voltage, 
            self.voltage_steps
        )
        
        for i, set_voltage in enumerate(voltage_points):
            # Check if measurement should stop
            if self.should_stop():
                break
            
            # Set power supply voltage
            self.channel.voltage = set_voltage
            
            # Allow settling time
            time.sleep(0.1)
            
            # Read actual power supply measurements
            measured_voltage = self.channel.measure_voltage
            measured_current = self.channel.measure_current
            
            # Acquire spectrum
            wavelengths = self.spectrometer.wavelengths()
            intensities = self.spectrometer.intensities()
            
            # Find peak wavelength and intensity
            peak_index = np.argmax(intensities)
            peak_wavelength = wavelengths[peak_index]
            peak_intensity = intensities[peak_index]
            
            # Emit results
            data = {
                '设定电压 (V)': set_voltage,
                '实测电压 (V)': measured_voltage,
                '实测电流 (A)': measured_current,
                '峰值波长 (nm)': peak_wavelength,
                '峰值强度 (arb.)': peak_intensity
            }
            
            self.emit('results', data)
            
            # Update progress
            progress = (i + 1) / len(voltage_points) * 100
            self.emit('progress', progress)
    
    def shutdown(self):
        """
        Safely shutdown instruments after measurement completion.
        """
        try:
            if hasattr(self, 'power_supply'):
                self.power_supply.shutdown()
        except Exception as e:
            print(f"Error shutting down power supply: {e}")
        
        try:
            if hasattr(self, 'spectrometer'):
                self.spectrometer.close()
        except Exception as e:
            print(f"Error closing spectrometer: {e}")


class MainWindow(ManagedWindow):
    """
    Main application window for the spectroscopy measurement system.
    """
    
    def __init__(self):
        super().__init__(
            procedure_class=SpectroscopyProcedure,
            inputs=[
                'start_voltage', 
                'stop_voltage', 
                'voltage_steps', 
                'integration_time',
                'channel_number'
            ],
            displays=[
                'start_voltage', 
                'stop_voltage', 
                'voltage_steps', 
                'integration_time',
                'channel_number'
            ],
            x_axis='设定电压 (V)',
            y_axis='峰值强度 (arb.)'
        )
        self.setWindowTitle('Spectroscopy Measurement System')


if __name__ == "__main__":
    # Create QApplication instance
    app = QApplication(sys.argv)
    
    # Create and show main window
    window = MainWindow()
    window.show()
    
    # Start application event loop
    sys.exit(app.exec())
