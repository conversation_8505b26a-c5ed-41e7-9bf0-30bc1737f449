"""
Test script for the improved Siglent SPD4000X power supply driver.

This script demonstrates the key improvements:
1. Channel-specific voltage and current ranges
2. Smooth voltage transitions
3. Proper multi-channel support
4. Enhanced error handling
"""

import sys
import time
from unittest.mock import Mock, MagicMock

# Mock PyMeasure for testing without actual hardware
sys.modules['pymeasure'] = Mock()
sys.modules['pymeasure.instruments'] = Mock()

# Create mock classes
class MockInstrument:
    def __init__(self, adapter, name="Mock Instrument", **kwargs):
        self.adapter = adapter
        self.name = name
        self.kwargs = kwargs
        self._commands = []
        self._responses = {}

    def write(self, command):
        self._commands.append(command)
        print(f"WRITE: {command}")

    def ask(self, command):
        self._commands.append(command)
        response = self._responses.get(command, "0.0")
        print(f"ASK: {command} -> {response}")
        return response

    def set_response(self, command, response):
        self._responses[command] = response

    @staticmethod
    def control(get_command, set_command, doc="", **kwargs):
        """Mock control property creator."""
        def getter(self):
            return float(self.ask(get_command.format(ch=self.id)))

        def setter(self, value):
            self.write(set_command.format(ch=self.id) % value)

        return property(getter, setter)

    @staticmethod
    def measurement(command, doc="", **kwargs):
        """Mock measurement property creator."""
        def getter(self):
            return float(self.ask(command.format(ch=self.id)))

        return property(getter)

class MockChannel:
    def __init__(self, parent, id, **kwargs):
        self.parent = parent
        self.id = id
        self.kwargs = kwargs
    
    def write(self, command):
        return self.parent.write(command)
    
    def ask(self, command):
        return self.parent.ask(command)

class MockMultiChannelCreator:
    def __init__(self, channel_class, channel_ids):
        self.channel_class = channel_class
        self.channel_ids = channel_ids
    
    def __get__(self, instance, owner):
        if instance is None:
            return self
        
        if not hasattr(instance, '_channels'):
            instance._channels = {}
            for ch_id in self.channel_ids:
                instance._channels[ch_id] = self.channel_class(instance, ch_id)
        
        return instance._channels

# Set up mocks
sys.modules['pymeasure.instruments'].Instrument = MockInstrument
sys.modules['pymeasure.instruments'].Channel = MockChannel
MockInstrument.MultiChannelCreator = MockMultiChannelCreator

# Now import our driver
from siglent_spd import SiglentSPD4000X, SiglentSPD1X


def test_channel_ranges():
    """Test that channels have correct voltage and current ranges."""
    print("=== Testing Channel Ranges ===")
    
    # Create mock power supply
    psu = SiglentSPD4000X("mock://test")
    
    # Test channel 1 (high precision)
    ch1_specs = psu.get_channel_specs(1)
    print(f"Channel 1: {ch1_specs}")
    assert ch1_specs['voltage_range'] == (0, 15.0)
    assert ch1_specs['current_range'] == (0, 1.5)
    assert ch1_specs['type'] == 'High Precision'
    
    # Test channel 2 (high current)
    ch2_specs = psu.get_channel_specs(2)
    print(f"Channel 2: {ch2_specs}")
    assert ch2_specs['voltage_range'] == (0, 12.0)
    assert ch2_specs['current_range'] == (0, 10.0)
    assert ch2_specs['type'] == 'High Current'
    
    print("✓ Channel ranges test passed")


def test_voltage_validation():
    """Test voltage range validation for different channels."""
    print("\n=== Testing Voltage Validation ===")
    
    psu = SiglentSPD4000X("mock://test")
    
    # Test valid voltage for channel 1 (0-15V)
    try:
        psu.channels[1].voltage = 10.0
        print("✓ Valid voltage for CH1 accepted")
    except ValueError as e:
        print(f"✗ Unexpected error: {e}")
    
    # Test invalid voltage for channel 1 (>15V)
    try:
        psu.channels[1].voltage = 20.0
        print("✗ Invalid voltage for CH1 should have been rejected")
    except ValueError as e:
        print(f"✓ Invalid voltage for CH1 correctly rejected: {e}")
    
    # Test valid voltage for channel 2 (0-12V)
    try:
        psu.channels[2].voltage = 10.0
        print("✓ Valid voltage for CH2 accepted")
    except ValueError as e:
        print(f"✗ Unexpected error: {e}")
    
    # Test invalid voltage for channel 2 (>12V)
    try:
        psu.channels[2].voltage = 15.0
        print("✗ Invalid voltage for CH2 should have been rejected")
    except ValueError as e:
        print(f"✓ Invalid voltage for CH2 correctly rejected: {e}")


def test_current_validation():
    """Test current range validation for different channels."""
    print("\n=== Testing Current Validation ===")
    
    psu = SiglentSPD4000X("mock://test")
    
    # Test valid current for channel 1 (0-1.5A)
    try:
        psu.channels[1].current_limit = 1.0
        print("✓ Valid current for CH1 accepted")
    except ValueError as e:
        print(f"✗ Unexpected error: {e}")
    
    # Test invalid current for channel 1 (>1.5A)
    try:
        psu.channels[1].current_limit = 2.0
        print("✗ Invalid current for CH1 should have been rejected")
    except ValueError as e:
        print(f"✓ Invalid current for CH1 correctly rejected: {e}")
    
    # Test valid current for channel 2 (0-10A)
    try:
        psu.channels[2].current_limit = 5.0
        print("✓ Valid current for CH2 accepted")
    except ValueError as e:
        print(f"✗ Unexpected error: {e}")
    
    # Test invalid current for channel 2 (>10A)
    try:
        psu.channels[2].current_limit = 15.0
        print("✗ Invalid current for CH2 should have been rejected")
    except ValueError as e:
        print(f"✓ Invalid current for CH2 correctly rejected: {e}")


def test_smooth_voltage_transitions():
    """Test smooth voltage transition functionality."""
    print("\n=== Testing Smooth Voltage Transitions ===")
    
    psu = SiglentSPD4000X("mock://test")
    
    # Set initial voltage response
    psu.set_response("CH1:VOLTage?", "2.0")
    
    print("Setting voltage from 2V to 8V (should use smooth transition)...")
    start_time = time.time()
    psu.channels[1].voltage = 8.0
    end_time = time.time()
    
    # Check that it took some time (indicating smooth transition)
    duration = end_time - start_time
    print(f"Transition took {duration:.2f} seconds")
    
    # Check commands sent
    voltage_commands = [cmd for cmd in psu._commands if "VOLTage" in cmd and "?" not in cmd]
    print(f"Voltage commands sent: {len(voltage_commands)}")
    
    if len(voltage_commands) > 1:
        print("✓ Smooth voltage transition implemented")
    else:
        print("✓ Direct voltage setting (small change)")


def test_backward_compatibility():
    """Test backward compatibility with SPD1X class."""
    print("\n=== Testing Backward Compatibility ===")
    
    # Test SPD1X class (should only have 2 channels)
    psu_1x = SiglentSPD1X("mock://test")
    
    # Check that it has only 2 channels
    if len(psu_1x.channels) == 2:
        print("✓ SPD1X has correct number of channels (2)")
    else:
        print(f"✗ SPD1X should have 2 channels, got {len(psu_1x.channels)}")
    
    # Check that channels 1 and 2 exist
    if 1 in psu_1x.channels and 2 in psu_1x.channels:
        print("✓ SPD1X channels 1 and 2 accessible")
    else:
        print("✗ SPD1X channels not properly accessible")


def main():
    """Run all tests."""
    print("Testing Improved Siglent SPD4000X Driver")
    print("=" * 50)
    
    try:
        test_channel_ranges()
        test_voltage_validation()
        test_current_validation()
        test_smooth_voltage_transitions()
        test_backward_compatibility()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed! The improved driver is working correctly.")
        print("\nKey improvements demonstrated:")
        print("- Channel-specific voltage and current ranges")
        print("- Proper validation for each channel type")
        print("- Smooth voltage transitions for large changes")
        print("- Backward compatibility with SPD1X series")
        print("- Enhanced error handling and user feedback")
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
